import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { z } from "zod";
import { db } from "~/server/db";
import { Decimal } from "@prisma/client/runtime/library";

// Base interfaces for Saudi Daily Information components

export interface SaudiWell {
  id: string;
  name: string;
  userId: string;
  type: number | null;
  latitude: Decimal | null;
  longitude: Decimal | null;
  startDate: Date | null;
  country: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaudiBitData {
  id: string;
  dailyInformationId: string;
  bitNumber: number | null;
  mdIn: Decimal | null;
  mdOut: Decimal | null;
  runFootage: Decimal | null;
  hours: Decimal | null;
  averageROP: Decimal | null;
  wob: number | null;
  rpm: number | null;
  iadc: string | null;
  size: Decimal | null;
  manufacturer: string | null;
  pressure: Decimal | null;
  gpm: number | null;
  jetVel: number | null;
  dpAv: Decimal | null;
  dcAv: Decimal | null;
  bitHhp: number | null;
  iRow: number | null;
  oRow: number | null;
  dc: string | null;
  location: string | null;
  bearings: string | null;
  serialNumber: string | null;
  type: string | null;
  jets: string | null;
  tfa: Decimal | null;
  gauge: number | null;
  other: string | null;
  poohReasons: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaudiMiscellaneus {
  id: string;
  dailyInformationId: string;
  bopTest: number | null;
  bopDrills: number | null;
  wind: string | null;
  sea: string | null;
  wheater: string | null;
  dslta: Decimal | null;
  safetyMeeting: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaudiMudData {
  id: string;
  dailyInformationId: string;
  weight: Decimal | null;
  flTemp: Decimal | null;
  funnel: Decimal | null;
  cakeHthp: Decimal | null;
  filtrateHthp: Decimal | null;
  cakeApi: Decimal | null;
  filtrateApi: Decimal | null;
  waterVol: Decimal | null;
  pv: Decimal | null;
  oilVol: Decimal | null;
  yp: Decimal | null;
  solidsVol: Decimal | null;
  elecStability: Decimal | null;
  sandVol: Decimal | null;
  rpm3: Decimal | null;
  rpm6: Decimal | null;
  lgs: Decimal | null;
  gelsSec: number | null;
  gelsMin: number | null;
  mbt: number | null;
  ph: Decimal | null;
  mudType: string | null;
  cappm: Decimal | null;
  clppm: Decimal | null;
  pptsSpurt: Decimal | null;
  pptTotal: Decimal | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaudiRepair {
  id: string;
  dailyInformationId: string;
  instrumentation: string | null;
  other: string | null;
  computerCommunicationVsatIssues: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaudiTruckBoats {
  id: string;
  dailyInformationId: string;
  standbyTankers: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaudiBulk {
  id: string;
  dailyInformationId: string;
  drillWtrBbls: number | null;
  potWtrBbls: number | null;
  fuelBbls: number | null;
  bariteSx: number | null;
  bentonine: number | null;
  cementGSx: number | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaudiDrillString {
  id: string;
  dailyInformationId: string;
  float: string | null;
  bhaHours: Decimal | null;
  stringWt: number | null;
  pickUp: number | null;
  slackOff: number | null;
  rotTorque: number | null;
  jasrSerial: Decimal | null;
  jarsHours: Decimal | null;
  shockSubSerial: string | null;
  shockSubHours: Decimal | null;
  mudMotorSerial: string | null;
  mudMotorHours: Decimal | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaudiPersonnel {
  id: string;
  dailyInformationId: string;
  company: string | null;
  category: string | null;
  numberOfPersons: number | null;
  onLocHours: number | null;
  operatingHours: number | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaudiProjectData {
  id: string;
  dailyInformationId: string;
  charge: string | null;
  wellbores: string | null;
  thuraya: string | null;
  rigFormatVsat: string | null;
  last24HourOperation: string | null;
  next24HourPlan: string | null;
  location: string | null;
  nextLocation: string | null;
  currentDepth: Decimal | null;
  lastCsgSize: number | null;
  measureDepth: Decimal | null;
  totalVerticalDepth: Decimal | null;
  linerSize: Decimal | null;
  tol: Decimal | null;
  prevDepth: Decimal | null;
  daysSinceSpud: Decimal | null;
  commDate: number | null;
  circ: Decimal | null;
  footage: Decimal | null;
  distanceFromDHA: Decimal | null;
  createdAt: Date;
  updatedAt: Date;
}

// Main interface using the smaller interfaces
export interface SaudiDailyInformationWithRelations {
  id: string;
  wellId: string;
  ddrId: string;
  day: number | null;
  rig: string | null;
  ddrPage: number;
  createdAt: Date;
  updatedAt: Date;
  well: SaudiWell;
  bitData: SaudiBitData | null;
  miscellaneus: SaudiMiscellaneus | null;
  mudData: SaudiMudData | null;
  repair: SaudiRepair | null;
  truckBoats: SaudiTruckBoats | null;
  bulk: SaudiBulk | null;
  drillString: SaudiDrillString | null;
  personnel: SaudiPersonnel[];
  projectData: SaudiProjectData | null;
}


export const saudiDailyInformationRouter = createTRPCRouter({
  listSaudiDailyInformation: protectedProcedure
    .input(z.object({ userId: z.string() }))
    .query(async ({ input }) => {
      return db.saudiDailyInformation.findMany({
        where: {
          well: {
            userId: input.userId,
          },
        },
        include: {
          well: true,
          bitData: true,
          miscellaneus: true,
          mudData: true,
          repair: true,
          truckBoats: true,
          bulk: true,
          drillString: true,
          personnel: true,
          projectData: true,
        },
        orderBy: {
          day: "asc",
        },
      });
    }),
});
