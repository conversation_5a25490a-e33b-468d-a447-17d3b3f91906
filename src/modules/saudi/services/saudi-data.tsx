import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { z } from "zod";
import { db } from "~/server/db";

export interface SaudiDailyInformationWithRelations {
  id: string;
  wellId: string;
  ddrId: string;
  day: number | null;
  rig: string | null;
  ddrPage: number;
  createdAt: Date;
  updatedAt: Date;
  well: {
    id: string;
    name: string;
    userId: string;
    type: number | null;
    latitude: number | null;
    longitude: number | null;
    startDate: Date | null;
    country: string | null;
    createdAt: Date;
    updatedAt: Date;
  };
  bitData: {
    id: string;
    dailyInformationId: string;
    bitNumber: number | null;
    mdIn: number | null;
    mdOut: number | null;
    runFootage: number | null;
    hours: number | null;
    averageROP: number | null;
    wob: number | null;
    rpm: number | null;
    iadc: string | null;
    size: number | null;
    manufacturer: string | null;
    pressure: number | null;
    gpm: number | null;
    jetVel: number | null;
    dpAv: number | null;
    dcAv: number | null;
    bitHhp: number | null;
    iRow: number | null;
    oRow: number | null;
    dc: string | null;
    location: string | null;
    bearings: string | null;
    serialNumber: string | null;
    type: string | null;
    jets: string | null;
    tfa: number | null;
    gauge: number | null;
    other: string | null;
    poohReasons: string | null;
    createdAt: Date;
    updatedAt: Date;
  } | null;
  miscellaneus: {
    id: string;
    dailyInformationId: string;
    bopTest: number | null;
    bopDrills: number | null;
    wind: string | null;
    sea: string | null;
    wheater: string | null;
    dslta: number | null;
    safetyMeeting: string | null;
    createdAt: Date;
    updatedAt: Date;
  } | null;
  mudData: {
    id: string;
    dailyInformationId: string;
    weight: number | null;
    flTemp: number | null;
    funnel: number | null;
    cakeHthp: number | null;
    filtrateHthp: number | null;
    cakeApi: number | null;
    filtrateApi: number | null;
    waterVol: number | null;
    pv: number | null;
    oilVol: number | null;
    yp: number | null;
    solidsVol: number | null;
    elecStability: number | null;
    sandVol: number | null;
    rpm3: number | null;
    rpm6: number | null;
    lgs: number | null;
    gelsSec: number | null;
    gelsMin: number | null;
    mbt: number | null;
    ph: number | null;
    mudType: string | null;
    cappm: number | null;
    clppm: number | null;
    pptsSpurt: number | null;
    pptTotal: number | null;
    createdAt: Date;
    updatedAt: Date;
  } | null;
  repair: {
    id: string;
    dailyInformationId: string;
    instrumentation: string | null;
    other: string | null;
    computerCommunicationVsatIssues: string | null;
    createdAt: Date;
    updatedAt: Date;
  } | null;
  truckBoats: {
    id: string;
    dailyInformationId: string;
    standbyTankers: string | null;
    createdAt: Date;
    updatedAt: Date;
  } | null;
  bulk: {
    id: string;
    dailyInformationId: string;
    drillWtrBbls: number | null;
    potWtrBbls: number | null;
    fuelBbls: number | null;
    bariteSx: number | null;
    bentonine: number | null;
    cementGSx: number | null;
    createdAt: Date;
    updatedAt: Date;
  } | null;
  drillString: {
    id: string;
    dailyInformationId: string;
    float: string | null;
    bhaHours: number | null;
    stringWt: number | null;
    pickUp: number | null;
    slackOff: number | null;
    rotTorque: number | null;
    jasrSerial: number | null;
    jarsHours: number | null;
    shockSubSerial: string | null;
    shockSubHours: number | null;
    mudMotorSerial: string | null;
    mudMotorHours: number | null;
    createdAt: Date;
    updatedAt: Date;
  } | null;
  personnel: Array<{
    id: string;
    dailyInformationId: string;
    company: string | null;
    category: string | null;
    numberOfPersons: number | null;
    onLocHours: number | null;
    operatingHours: number | null;
    createdAt: Date;
    updatedAt: Date;
  }>;
  projectData: {
    id: string;
    dailyInformationId: string;
    charge: string | null;
    wellbores: string | null;
    thuraya: string | null;
    rigFormatVsat: string | null;
    last24HourOperation: string | null;
    next24HourPlan: string | null;
    location: string | null;
    nextLocation: string | null;
    currentDepth: number | null;
    lastCsgSize: number | null;
    measureDepth: number | null;
    totalVerticalDepth: number | null;
    linerSize: number | null;
    tol: number | null;
    prevDepth: number | null;
    daysSinceSpud: number | null;
    commDate: number | null;
    circ: number | null;
    footage: number | null;
    distanceFromDHA: number | null;
    createdAt: Date;
    updatedAt: Date;
  } | null;
}


export const saudiDailyInformationRouter = createTRPCRouter({
  listSaudiDailyInformation: protectedProcedure
    .input(z.object({ userId: z.string() }))
    .query(async ({ input }) => {
      return db.saudiDailyInformation.findMany({
        where: {
          well: {
            userId: input.userId,
          },
        },
        include: {
          well: true,
          bitData: true,
          miscellaneus: true,
          mudData: true,
          repair: true,
          truckBoats: true,
          bulk: true,
          drillString: true,
          personnel: true,
          projectData: true,
        },
        orderBy: {
          day: "asc",
        },
      });
    }),
});
