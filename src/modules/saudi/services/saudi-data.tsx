import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { z } from "zod";
import { db } from "~/server/db";

// export interface SaudiDailyInformationResponse {
//   id: string;
//   wellId: string;
//   ddrId: string;
//   day: number;
//   rig: string;
//   createdAt: string;
//   updatedAt: string;
//   bitData: SaudiBitData;
//   miscellaneus: SaudiMiscellaneus;
//   mudData: SaudiMudData;
//   repair: SaudiRepair;
//   truckBoats: SaudiTruckBoats;
//   bulk: SaudiBulk;
//   drillString: SaudiDrillString;
// }
//
// export interface SaudiBitData {
//   id: string;
//   bitNumber: number;
//   mdIn: number;
//   mdOut: number;
//   runFootage: number;
//   hours: number;
//   averageROP: number;
//   wob: number;
//   rpm: number;
//   iadc: string;
//   size: number;
//   manufacturer: string;
//   pressure: number;
//   gmp: number;
//   jetVel: number;
//   dpAv: number;
//   dcAv: number;
//   bitHhp: number;
//   iRow: number;
//   oRow: number;
//   dc: string;
//   location: string;
//   bearings: string;
//   serialNumber: string;
//   type: string;
//   jets: string;
//   tfa: number;
//   gauge: number;
//   other: string;
//   poohReasons: string;
// }
//
// export interface SaudiMiscellaneus {
//   id: string;
//   bopTest: number;
//   bopDrills: number;
//   wind: string;
//   sea: string;
//   wheater: string;
//   dslta: number;
//   safetyMeeting: string;
// }
//
// export interface SaudiMudData {
//   id: string;
//   weight: number;
//   flTemp: number;
//   funnel: number;
//   cakeHthp: number;
//   filtrateHthp: number;
//   cakeAPI: number;
//   filtrateApi: number;
//   waterVol: number;
//   pv: number;
//   oilVol: number;
//   yp: number;
//   solidsVol: number;
//   elecStability: number;
//   sandVol: number;
//   "36Rpm": number;
//   LGS: number;
//   gelsSec: number;
//   gelsMin: number;
//   mbt: number;
//   ph: number;
//   mudType: string;
//   cappm: string;
//   clppm: string;
//   pptsSpurt: string;
//   pptTotal: string;
// }
//
// export interface SaudiRepair {
//   id: string;
//   instrumentation: string;
//   other: string;
//   computerCommunicationVsatIssues: string;
// }
//
// export interface SaudiTruckBoats {
//   id: string;
//   standbyTankers: string;
// }
//
// export interface SaudiBulk {
//   id: string;
//   drillWtrBbls: number;
//   potWtrBbls: number;
//   fuelBbls: number;
//   bariteSx: number;
//   bentonine: number;
//   cementGSx: number;
// }
//
// export interface SaudiDrillString {
//   id: string;
//   float: string;
//   bhaHours: number;
//   stringWt: number;
//   pickUp: number;
//   slackOff: number;
//   rotTorque: number;
//   jasrSerial: number;
//   jarsHours: number;
//   shockSubSerial: string;
//   shockSubHours: number;
//   mudMotorSerial: string;
//   mudMotorHours: number;
//   drillStringTable: SaudiDrillStringTable[];
// }
//
// export interface SaudiDrillStringTable {
//   id: string;
//   order: number;
//   component: string;
//   provider: string;
//   nominalSize: number;
//   joints: number;
//   odSize: number;
//   idSize: number;
//   length: number;
//   topThread: string;
//   bottomThread: string;
//   weigth: number;
//   cumWeigth: number;
//   grade: string;
//   class: string;
//   serial: string;
// }

export const saudiDailyInformationRouter = createTRPCRouter({
  listSaudiDailyInformation: protectedProcedure
    .input(z.object({ userId: z.string() }))
    .query(async ({ input }) => {
      return db.saudiDailyInformation.findMany({
        where: {
          well: {
            userId: input.userId,
          },
        },
        include: {
          well: true,
          bitData: true,
          miscellaneus: true,
          mudData: true,
          repair: true,
          truckBoats: true,
          bulk: true,
          drillString: true,
          personnel: true,
          projectData: true,
        },
        orderBy: {
          day: "asc",
        },
      });
    }),
});
