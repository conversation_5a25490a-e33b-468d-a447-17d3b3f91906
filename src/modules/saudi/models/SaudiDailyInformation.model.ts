import {
  type SaudiDailyInformationResponse,
  type SaudiWellResponse,
  type SaudiBitDataResponse,
  type SaudiMiscellaneusResponse,
  type SaudiMudDataResponse,
  type SaudiRepairResponse,
  type SaudiTruckBoatsResponse,
  type SaudiBulkResponse,
  type SaudiDrillStringResponse,
  type SaudiPersonnelResponse,
  type SaudiProjectDataResponse
} from "~/modules/saudi/services/saudi-data";

// Model classes for Saudi Daily Information components
export class SaudiWell {
  constructor(
    public id: string,
    public name: string,
    public userId: string,
    public type: number | null,
    public latitude: number | null,
    public longitude: number | null,
    public startDate: Date | null,
    public country: string | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiWellResponse): SaudiWell {
    return new SaudiWell(
      response.id,
      response.name,
      response.userId,
      response.type,
      response.latitude?.toNumber() ?? null,
      response.longitude?.toNumber() ?? null,
      response.startDate,
      response.country,
      response.createdAt,
      response.updatedAt
    );
  }
}

export class SaudiBitData {
  constructor(
    public id: string,
    public dailyInformationId: string,
    public bitNumber: number | null,
    public mdIn: number | null,
    public mdOut: number | null,
    public runFootage: number | null,
    public hours: number | null,
    public averageROP: number | null,
    public wob: number | null,
    public rpm: number | null,
    public iadc: string | null,
    public size: number | null,
    public manufacturer: string | null,
    public pressure: number | null,
    public gpm: number | null,
    public jetVel: number | null,
    public dpAv: number | null,
    public dcAv: number | null,
    public bitHhp: number | null,
    public iRow: number | null,
    public oRow: number | null,
    public dc: string | null,
    public location: string | null,
    public bearings: string | null,
    public serialNumber: string | null,
    public type: string | null,
    public jets: string | null,
    public tfa: number | null,
    public gauge: number | null,
    public other: string | null,
    public poohReasons: string | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiBitDataResponse): SaudiBitData {
    return new SaudiBitData(
      response.id,
      response.dailyInformationId,
      response.bitNumber,
      response.mdIn,
      response.mdOut,
      response.runFootage,
      response.hours,
      response.averageROP,
      response.wob,
      response.rpm,
      response.iadc,
      response.size,
      response.manufacturer,
      response.pressure,
      response.gpm,
      response.jetVel,
      response.dpAv,
      response.dcAv,
      response.bitHhp,
      response.iRow,
      response.oRow,
      response.dc,
      response.location,
      response.bearings,
      response.serialNumber,
      response.type,
      response.jets,
      response.tfa,
      response.gauge,
      response.other,
      response.poohReasons,
      response.createdAt,
      response.updatedAt
    );
  }
}

export class SaudiMiscellaneus {
  constructor(
    public id: string,
    public dailyInformationId: string,
    public bopTest: number | null,
    public bopDrills: number | null,
    public wind: string | null,
    public sea: string | null,
    public wheater: string | null,
    public dslta: number | null,
    public safetyMeeting: string | null,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  static fromResponse(response: SaudiMiscellaneusResponse): SaudiMiscellaneus {
    return new SaudiMiscellaneus(
      response.id,
      response.dailyInformationId,
      response.bopTest,
      response.bopDrills,
      response.wind,
      response.sea,
      response.wheater,
      response.dslta,
      response.safetyMeeting,
      response.createdAt,
      response.updatedAt
    );
  }
}