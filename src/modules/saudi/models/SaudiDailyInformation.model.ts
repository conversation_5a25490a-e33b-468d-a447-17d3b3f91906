export class SaudiDailyInformation {

  constructor(saudiData: SaudiDailyInformationResponse) {
    this.mapToSaudiDailyInformation(saudiData)
  }

  mapToSaudiDailyInformation(saudiData: SaudiDailyInformationResponse): SaudiDailyInformation {
    console.log(saudiData)
      this.date = saudiData.day ?? ""
      this.well = saudiData.wellId ?? ""
      this.bitData = saudiData.bitData.bitNumber ?? ""
      this.bulk = saudiData.bulk.id ?? ""
      this.drillingString = saudiData.drillString.id ?? ""
      this.drillingStringTable = saudiData.drillString.drillStringTable ?? ""
      this.miscellaneous = saudiData.miscellaneus.id ?? ""
      this.mudData = saudiData.mudData.mudType ?? ""
      this.repair = saudiData.repair.id ?? ""
      this.mudTreatment = saudiData.mudData.mudType ?? ""
      this.truckBoats = saudiData.truckBoats.id ?? ""
  }
}